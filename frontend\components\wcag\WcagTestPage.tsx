/**
 * WCAG Test Page Component
 * Demonstration page showing all WCAG components working together
 */

'use client';

import React, { useState } from 'react';
import { WcagScanForm, WcagScanOverview, WcagScanProgress, WcagExportDialog } from './index';
import { WcagScanFormData, WcagScanResult } from '../../types/wcag';
import wcagApiService from '../../services/wcag-api';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';

const WcagTestPage: React.FC = () => {
  const [currentScan, setCurrentScan] = useState<WcagScanResult | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string>('');
  const [activeTab, setActiveTab] = useState<string>('form');
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportError, setExportError] = useState<string>('');

  /**
   * Handle scan form submission
   */
  const handleScanSubmit = async (formData: WcagScanFormData) => {
    try {
      setError('');
      setIsScanning(true);
      setActiveTab('progress');

      // Start the scan
      const scanResult = await wcagApiService.startScan(formData);
      setCurrentScan(scanResult);

      // If scan is already completed (unlikely but possible)
      if (scanResult.status === 'completed') {
        setIsScanning(false);
        setActiveTab('results');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start scan');
      setIsScanning(false);
      setActiveTab('form');
    }
  };

  /**
   * Handle scan completion
   */
  const handleScanComplete = async () => {
    if (currentScan) {
      try {
        // Fetch final scan results
        const finalResults = await wcagApiService.getScanDetails(currentScan.scanId);
        setCurrentScan(finalResults);
        setIsScanning(false);
        setActiveTab('results');
      } catch (err) {
        setError('Failed to fetch final scan results');
        setIsScanning(false);
      }
    }
  };

  /**
   * Handle scan cancellation
   */
  const handleScanCancel = () => {
    setCurrentScan(null);
    setIsScanning(false);
    setActiveTab('form');
  };

  /**
   * Load sample scan for demonstration (using real API)
   */
  const loadSampleScan = async () => {
    try {
      setError('');
      setIsScanning(true);
      setActiveTab('progress');

      // Use a real website for demonstration
      const sampleFormData: WcagScanFormData = {
        targetUrl: 'https://www.w3.org/WAI/WCAG21/quickref/',
        enableContrastAnalysis: true,
        enableKeyboardTesting: true,
        enableFocusAnalysis: true,
        enableSemanticValidation: true,
        enableManualReview: true,
        wcagVersion: '2.2',
        level: 'AA',
        maxPages: 3,
      };

      // Start a real scan
      const scanResult = await wcagApiService.startScan(sampleFormData);
      setCurrentScan(scanResult);

      // If scan is already completed (unlikely but possible)
      if (scanResult.status === 'completed') {
        setIsScanning(false);
        setActiveTab('results');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start sample scan');
      setIsScanning(false);
      setActiveTab('form');
    }
  };

  /**
   * Handle export button click
   */
  const handleExportClick = () => {
    setExportError('');
    setExportDialogOpen(true);
  };

  /**
   * Handle export execution
   */
  const handleExport = async (
    format: 'pdf' | 'json' | 'csv',
    options: {
      includeEvidence: boolean;
      includeRecommendations: boolean;
      includeManualReviewItems: boolean;
    },
  ) => {
    if (!currentScan) return;

    try {
      setIsExporting(true);
      setExportError('');

      // Use the download export function which handles file download
      await wcagApiService.downloadExport(currentScan.scanId, format, options);
    } catch (err) {
      setExportError(err instanceof Error ? err.message : 'Export failed');
      throw err; // Re-throw to let dialog handle it
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>WCAG Components Test Page</CardTitle>
          <p className="text-sm text-muted-foreground">
            Test and demonstrate WCAG compliance scanning components
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-4">
            <button
              onClick={loadSampleScan}
              disabled={isScanning}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isScanning ? 'Starting Sample Scan...' : 'Start Sample Scan'}
            </button>
            <button
              onClick={() => {
                setCurrentScan(null);
                setIsScanning(false);
                setActiveTab('form');
                setError('');
              }}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              Reset
            </button>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="form">Scan Form</TabsTrigger>
              <TabsTrigger value="progress" disabled={!isScanning && !currentScan}>
                Progress
              </TabsTrigger>
              <TabsTrigger
                value="results"
                disabled={!currentScan || currentScan.status !== 'completed'}
              >
                Results
              </TabsTrigger>
            </TabsList>

            <TabsContent value="form" className="mt-6">
              <WcagScanForm onSubmit={handleScanSubmit} isLoading={isScanning} error={error} />
            </TabsContent>

            <TabsContent value="progress" className="mt-6">
              {currentScan && (
                <WcagScanProgress
                  scanId={currentScan.scanId}
                  onComplete={handleScanComplete}
                  onCancel={handleScanCancel}
                />
              )}
            </TabsContent>

            <TabsContent value="results" className="mt-6">
              {currentScan && currentScan.status === 'completed' && (
                <WcagScanOverview
                  scanResult={currentScan}
                  onExport={handleExportClick}
                  loading={isExporting}
                />
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Export Dialog */}
      {currentScan && (
        <WcagExportDialog
          open={exportDialogOpen}
          onClose={() => setExportDialogOpen(false)}
          onExport={handleExport}
          scanId={currentScan.scanId}
          isLoading={isExporting}
          error={exportError}
        />
      )}
    </div>
  );
};

export default WcagTestPage;
