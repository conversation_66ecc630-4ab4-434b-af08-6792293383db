/**
 * Media-Heavy Site Analyzer for WCAG Scanning
 * Specialized analysis for video players, image galleries, audio content, and multimedia accessibility
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';

export interface MediaElement {
  type: 'video' | 'audio' | 'image' | 'gallery' | 'slideshow' | 'iframe' | 'embed';
  selector: string;
  src?: string;
  hasAccessibleControls: boolean;
  hasTranscript: boolean;
  hasCaptions: boolean;
  hasAudioDescription: boolean;
  hasAltText: boolean;
  hasProperLabeling: boolean;
  isKeyboardAccessible: boolean;
  accessibilityScore: number; // 0-100
  issues: string[];
  recommendations: string[];
  mediaInfo: {
    duration?: number;
    format?: string;
    size?: { width: number; height: number };
    autoplay: boolean;
    controls: boolean;
  };
}

export interface MediaAnalysisResult {
  totalMediaElements: number;
  accessibleElements: number;
  videoElements: MediaElement[];
  audioElements: MediaElement[];
  imageElements: MediaElement[];
  galleryElements: MediaElement[];
  embeddedContent: MediaElement[];
  overallScore: number;
  criticalIssues: string[];
  recommendations: string[];
  complianceLevel: 'A' | 'AA' | 'AAA' | 'fail';
}

export interface MediaAnalysisConfig {
  analyzeVideos: boolean;
  analyzeAudio: boolean;
  analyzeImages: boolean;
  analyzeGalleries: boolean;
  analyzeEmbeddedContent: boolean;
  checkTranscripts: boolean;
  checkCaptions: boolean;
  checkAudioDescriptions: boolean;
  checkKeyboardNavigation: boolean;
  deepAnalysis: boolean;
}

/**
 * Advanced media accessibility analyzer
 */
export class MediaAnalyzer {
  private static instance: MediaAnalyzer;

  private constructor() {}

  static getInstance(): MediaAnalyzer {
    if (!MediaAnalyzer.instance) {
      MediaAnalyzer.instance = new MediaAnalyzer();
    }
    return MediaAnalyzer.instance;
  }

  /**
   * Analyze media-heavy site accessibility
   */
  async analyzeMediaSite(
    page: Page,
    config: Partial<MediaAnalysisConfig> = {},
  ): Promise<MediaAnalysisResult> {
    const fullConfig: MediaAnalysisConfig = {
      analyzeVideos: config.analyzeVideos ?? true,
      analyzeAudio: config.analyzeAudio ?? true,
      analyzeImages: config.analyzeImages ?? true,
      analyzeGalleries: config.analyzeGalleries ?? true,
      analyzeEmbeddedContent: config.analyzeEmbeddedContent ?? true,
      checkTranscripts: config.checkTranscripts ?? true,
      checkCaptions: config.checkCaptions ?? true,
      checkAudioDescriptions: config.checkAudioDescriptions ?? true,
      checkKeyboardNavigation: config.checkKeyboardNavigation ?? true,
      deepAnalysis: config.deepAnalysis ?? true,
    };

    logger.debug('🎬 Starting media accessibility analysis');

    // Inject media analysis functions
    await this.injectMediaAnalysisFunctions(page);

    // Analyze different media types
    const videoElements = fullConfig.analyzeVideos
      ? await this.analyzeVideoElements(page, fullConfig)
      : [];
    const audioElements = fullConfig.analyzeAudio
      ? await this.analyzeAudioElements(page, fullConfig)
      : [];
    const imageElements = fullConfig.analyzeImages
      ? await this.analyzeImageElements(page, fullConfig)
      : [];
    const galleryElements = fullConfig.analyzeGalleries
      ? await this.analyzeGalleryElements(page, fullConfig)
      : [];
    const embeddedContent = fullConfig.analyzeEmbeddedContent
      ? await this.analyzeEmbeddedContent(page, fullConfig)
      : [];

    // Calculate overall results
    const result = this.calculateOverallResults(
      videoElements,
      audioElements,
      imageElements,
      galleryElements,
      embeddedContent,
    );

    logger.info(
      `✅ Media analysis completed: ${result.totalMediaElements} elements analyzed (score: ${result.overallScore})`,
      {
        videos: videoElements.length,
        audio: audioElements.length,
        images: imageElements.length,
        galleries: galleryElements.length,
        embedded: embeddedContent.length,
        complianceLevel: result.complianceLevel,
      },
    );

    return result;
  }

  /**
   * Inject media analysis functions
   */
  private async injectMediaAnalysisFunctions(page: Page): Promise<void> {
    await page.evaluateOnNewDocument(() => {
      (window as any).wcagMediaAnalysis = {
        /**
         * Get element selector
         */
        getElementSelector(element: HTMLElement): string {
          if (element.id) return `#${element.id}`;

          const path = [];
          let current = element;

          while (current && current.nodeType === Node.ELEMENT_NODE && current !== document.body) {
            let selector = current.nodeName.toLowerCase();

            if (current.className) {
              const classes = current.className.split(' ').filter((c) => c.trim());
              if (classes.length > 0) {
                selector += '.' + classes.slice(0, 2).join('.');
              }
            }

            path.unshift(selector);
            current = current.parentElement!;

            if (path.length > 4) break;
          }

          return path.join(' > ');
        },

        /**
         * Check for captions/subtitles
         */
        hasCaptions(videoElement: HTMLVideoElement): boolean {
          // Check for track elements
          const tracks = videoElement.querySelectorAll(
            'track[kind="captions"], track[kind="subtitles"]',
          );
          if (tracks.length > 0) return true;

          // Check for common caption indicators
          const captionIndicators = [
            '[class*="caption"]',
            '[class*="subtitle"]',
            '[class*="cc"]',
            '[aria-label*="caption"]',
            '[title*="caption"]',
          ];

          return captionIndicators.some(
            (selector) => videoElement.parentElement?.querySelector(selector) !== null,
          );
        },

        /**
         * Check for transcript
         */
        hasTranscript(mediaElement: HTMLElement): boolean {
          // Look for transcript in nearby elements
          const transcriptSelectors = [
            '[class*="transcript"]',
            '[id*="transcript"]',
            '[aria-label*="transcript"]',
            '.text-alternative',
            '.media-transcript',
          ];

          // Check within parent container
          const container =
            mediaElement.closest('.media-container, .video-container, .audio-container') ||
            mediaElement.parentElement;

          if (container) {
            return transcriptSelectors.some(
              (selector) => container.querySelector(selector) !== null,
            );
          }

          return false;
        },

        /**
         * Check for audio description
         */
        hasAudioDescription(videoElement: HTMLVideoElement): boolean {
          // Check for audio description tracks
          const adTracks = videoElement.querySelectorAll('track[kind="descriptions"]');
          if (adTracks.length > 0) return true;

          // Check for audio description indicators
          const adIndicators = [
            '[class*="audio-description"]',
            '[class*="described"]',
            '[aria-label*="audio description"]',
            '[title*="audio description"]',
          ];

          return adIndicators.some(
            (selector) => videoElement.parentElement?.querySelector(selector) !== null,
          );
        },

        /**
         * Check keyboard accessibility
         */
        isKeyboardAccessible(element: HTMLElement): boolean {
          // Check if element is focusable
          const tabIndex = element.getAttribute('tabindex');
          const isFocusable =
            tabIndex !== '-1' &&
            (tabIndex !== null ||
              ['video', 'audio', 'button', 'a'].includes(element.tagName.toLowerCase()));

          // Check for keyboard event handlers
          const hasKeyHandlers =
            element.onkeydown !== null || element.onkeyup !== null || element.onkeypress !== null;

          return isFocusable || hasKeyHandlers;
        },

        /**
         * Analyze media controls
         */
        analyzeMediaControls(mediaElement: HTMLVideoElement | HTMLAudioElement): {
          hasAccessibleControls: boolean;
          issues: string[];
        } {
          const issues: string[] = [];

          // Check if controls are present
          if (
            !mediaElement.controls &&
            !mediaElement.parentElement?.querySelector('.custom-controls')
          ) {
            issues.push('Media element lacks accessible controls');
          }

          // Check for custom controls accessibility
          const customControls = mediaElement.parentElement?.querySelectorAll(
            '.control, [role="button"]',
          );
          if (customControls) {
            customControls.forEach((control) => {
              const button = control as HTMLElement;
              if (!button.getAttribute('aria-label') && !button.textContent?.trim()) {
                issues.push('Media control button lacks accessible label');
              }
            });
          }

          // Check for autoplay (accessibility concern)
          if (mediaElement.autoplay) {
            issues.push('Media autoplays which can be disorienting');
          }

          return {
            hasAccessibleControls: issues.length === 0,
            issues,
          };
        },

        /**
         * Get media information
         */
        getMediaInfo(mediaElement: HTMLVideoElement | HTMLAudioElement): {
          duration?: number;
          format?: string;
          size?: { width: number; height: number };
          autoplay: boolean;
          controls: boolean;
        } {
          return {
            duration: mediaElement.duration || undefined,
            format: mediaElement.currentSrc
              ? mediaElement.currentSrc.split('.').pop()?.toLowerCase()
              : undefined,
            size:
              mediaElement.tagName === 'VIDEO'
                ? {
                    width:
                      (mediaElement as HTMLVideoElement).videoWidth || mediaElement.clientWidth,
                    height:
                      (mediaElement as HTMLVideoElement).videoHeight || mediaElement.clientHeight,
                  }
                : undefined,
            autoplay: mediaElement.autoplay,
            controls: mediaElement.controls,
          };
        },

        /**
         * Calculate accessibility score
         */
        calculateAccessibilityScore(element: MediaElement): number {
          let score = 100;

          // Deduct points for missing features
          if (!element.hasAccessibleControls) score -= 20;
          if (!element.hasProperLabeling) score -= 15;
          if (!element.isKeyboardAccessible) score -= 15;

          // Media-specific deductions
          if (element.type === 'video') {
            if (!element.hasCaptions) score -= 25;
            if (!element.hasAudioDescription) score -= 15;
            if (!element.hasTranscript) score -= 10;
          } else if (element.type === 'audio') {
            if (!element.hasTranscript) score -= 30;
          } else if (element.type === 'image') {
            if (!element.hasAltText) score -= 40;
          }

          // Deduct for each issue
          score -= element.issues.length * 5;

          return Math.max(0, score);
        },

        /**
         * Generate recommendations
         */
        generateRecommendations(element: MediaElement): string[] {
          const recommendations: string[] = [];

          if (element.type === 'video') {
            if (!element.hasCaptions) {
              recommendations.push('Add captions or subtitles for video content');
            }
            if (!element.hasAudioDescription) {
              recommendations.push('Provide audio descriptions for visual content');
            }
            if (!element.hasTranscript) {
              recommendations.push('Include a full transcript of video content');
            }
          }

          if (element.type === 'audio') {
            if (!element.hasTranscript) {
              recommendations.push('Provide a complete transcript for audio content');
            }
          }

          if (element.type === 'image') {
            if (!element.hasAltText) {
              recommendations.push('Add descriptive alt text to images');
            }
          }

          if (!element.hasAccessibleControls) {
            recommendations.push('Ensure media controls are keyboard accessible');
          }

          if (!element.isKeyboardAccessible) {
            recommendations.push('Make media element keyboard navigable');
          }

          return recommendations;
        },
      };
    });
  }

  /**
   * Analyze video elements
   */
  private async analyzeVideoElements(
    page: Page,
    config: MediaAnalysisConfig,
  ): Promise<MediaElement[]> {
    return await page.evaluate((config) => {
      const videoElements: MediaElement[] = [];
      const videos = document.querySelectorAll('video');

      videos.forEach((video) => {
        const analysis = (window as any).wcagMediaAnalysis;
        const controlsAnalysis = analysis.analyzeMediaControls(video);

        const element: MediaElement = {
          type: 'video',
          selector: analysis.getElementSelector(video),
          src: video.currentSrc || video.src,
          hasAccessibleControls: controlsAnalysis.hasAccessibleControls,
          hasTranscript: config.checkTranscripts ? analysis.hasTranscript(video) : true,
          hasCaptions: config.checkCaptions ? analysis.hasCaptions(video) : true,
          hasAudioDescription: config.checkAudioDescriptions
            ? analysis.hasAudioDescription(video)
            : true,
          hasAltText: true, // Videos don't need alt text
          hasProperLabeling: !!(video.getAttribute('aria-label') || video.getAttribute('title')),
          isKeyboardAccessible: config.checkKeyboardNavigation
            ? analysis.isKeyboardAccessible(video)
            : true,
          accessibilityScore: 0,
          issues: controlsAnalysis.issues,
          recommendations: [],
          mediaInfo: analysis.getMediaInfo(video),
        };

        element.accessibilityScore = analysis.calculateAccessibilityScore(element);
        element.recommendations = analysis.generateRecommendations(element);

        videoElements.push(element);
      });

      return videoElements;
    }, config);
  }

  /**
   * Analyze audio elements
   */
  private async analyzeAudioElements(
    page: Page,
    config: MediaAnalysisConfig,
  ): Promise<MediaElement[]> {
    return await page.evaluate((config) => {
      const audioElements: MediaElement[] = [];
      const audios = document.querySelectorAll('audio');

      audios.forEach((audio) => {
        const analysis = (window as any).wcagMediaAnalysis;
        const controlsAnalysis = analysis.analyzeMediaControls(audio);

        const element: MediaElement = {
          type: 'audio',
          selector: analysis.getElementSelector(audio),
          src: audio.currentSrc || audio.src,
          hasAccessibleControls: controlsAnalysis.hasAccessibleControls,
          hasTranscript: config.checkTranscripts ? analysis.hasTranscript(audio) : true,
          hasCaptions: true, // Audio doesn't need captions
          hasAudioDescription: true, // Audio doesn't need audio description
          hasAltText: true, // Audio doesn't need alt text
          hasProperLabeling: !!(audio.getAttribute('aria-label') || audio.getAttribute('title')),
          isKeyboardAccessible: config.checkKeyboardNavigation
            ? analysis.isKeyboardAccessible(audio)
            : true,
          accessibilityScore: 0,
          issues: controlsAnalysis.issues,
          recommendations: [],
          mediaInfo: analysis.getMediaInfo(audio),
        };

        element.accessibilityScore = analysis.calculateAccessibilityScore(element);
        element.recommendations = analysis.generateRecommendations(element);

        audioElements.push(element);
      });

      return audioElements;
    }, config);
  }

  /**
   * Analyze image elements
   */
  private async analyzeImageElements(
    page: Page,
    config: MediaAnalysisConfig,
  ): Promise<MediaElement[]> {
    return await page.evaluate(() => {
      const imageElements: MediaElement[] = [];
      const images = document.querySelectorAll('img');

      images.forEach((img) => {
        const analysis = (window as any).wcagMediaAnalysis;
        const issues: string[] = [];

        // Check alt text
        const hasAltText = img.hasAttribute('alt');
        if (!hasAltText) {
          issues.push('Image missing alt attribute');
        } else if (img.alt.trim() === '') {
          // Empty alt is okay for decorative images, but check context
          const isDecorative =
            img.getAttribute('role') === 'presentation' ||
            img.getAttribute('aria-hidden') === 'true';
          if (!isDecorative) {
            issues.push('Image has empty alt text but may not be decorative');
          }
        }

        const element: MediaElement = {
          type: 'image',
          selector: analysis.getElementSelector(img),
          src: img.src,
          hasAccessibleControls: true, // Images don't have controls
          hasTranscript: true, // Images don't need transcripts
          hasCaptions: true, // Images don't need captions
          hasAudioDescription: true, // Images don't need audio description
          hasAltText,
          hasProperLabeling: hasAltText,
          isKeyboardAccessible: true, // Images are typically not interactive
          accessibilityScore: 0,
          issues,
          recommendations: [],
          mediaInfo: {
            size: {
              width: img.naturalWidth || img.clientWidth,
              height: img.naturalHeight || img.clientHeight,
            },
            format: img.src.split('.').pop()?.toLowerCase(),
            autoplay: false,
            controls: false,
          },
        };

        element.accessibilityScore = analysis.calculateAccessibilityScore(element);
        element.recommendations = analysis.generateRecommendations(element);

        imageElements.push(element);
      });

      return imageElements;
    });
  }

  /**
   * Analyze gallery elements
   */
  private async analyzeGalleryElements(
    page: Page,
    config: MediaAnalysisConfig,
  ): Promise<MediaElement[]> {
    return await page.evaluate((config) => {
      const galleryElements: MediaElement[] = [];

      // Common gallery selectors
      const gallerySelectors = [
        '.gallery',
        '.slideshow',
        '.carousel',
        '[class*="gallery"]',
        '[class*="slideshow"]',
        '[role="region"][aria-label*="gallery"]',
      ];

      gallerySelectors.forEach((selector) => {
        const galleries = document.querySelectorAll(selector);
        galleries.forEach((gallery) => {
          const analysis = (window as any).wcagMediaAnalysis;
          const issues: string[] = [];

          // Check for keyboard navigation
          const hasKeyboardNav = config.checkKeyboardNavigation
            ? analysis.isKeyboardAccessible(gallery as HTMLElement)
            : true;

          if (!hasKeyboardNav) {
            issues.push('Gallery not keyboard accessible');
          }

          // Check for proper labeling
          const hasProperLabeling = !!(
            gallery.getAttribute('aria-label') || gallery.getAttribute('aria-labelledby')
          );

          if (!hasProperLabeling) {
            issues.push('Gallery lacks proper ARIA labeling');
          }

          // Check for navigation controls
          const hasNavControls = gallery.querySelector(
            '.prev, .next, [aria-label*="previous"], [aria-label*="next"]',
          );
          if (!hasNavControls) {
            issues.push('Gallery lacks accessible navigation controls');
          }

          const element: MediaElement = {
            type: 'gallery',
            selector: analysis.getElementSelector(gallery as HTMLElement),
            hasAccessibleControls: !!hasNavControls,
            hasTranscript: true, // Galleries don't need transcripts
            hasCaptions: true, // Galleries don't need captions
            hasAudioDescription: true, // Galleries don't need audio description
            hasAltText: true, // Checked per image
            hasProperLabeling,
            isKeyboardAccessible: hasKeyboardNav,
            accessibilityScore: 0,
            issues,
            recommendations: [],
            mediaInfo: {
              autoplay: false,
              controls: !!hasNavControls,
            },
          };

          element.accessibilityScore = analysis.calculateAccessibilityScore(element);
          element.recommendations = analysis.generateRecommendations(element);

          galleryElements.push(element);
        });
      });

      return galleryElements;
    }, config);
  }

  /**
   * Analyze embedded content
   */
  private async analyzeEmbeddedContent(
    page: Page,
    config: MediaAnalysisConfig,
  ): Promise<MediaElement[]> {
    return await page.evaluate((config) => {
      const embeddedElements: MediaElement[] = [];

      // Analyze iframes and embedded content
      const embeds = document.querySelectorAll('iframe, embed, object');

      embeds.forEach((embed) => {
        const analysis = (window as any).wcagMediaAnalysis;
        const issues: string[] = [];

        // Check for proper labeling
        const hasProperLabeling = !!(
          embed.getAttribute('title') ||
          embed.getAttribute('aria-label') ||
          embed.getAttribute('aria-labelledby')
        );

        if (!hasProperLabeling) {
          issues.push('Embedded content lacks descriptive title or label');
        }

        // Check if it's keyboard accessible
        const isKeyboardAccessible = config.checkKeyboardNavigation
          ? analysis.isKeyboardAccessible(embed as HTMLElement)
          : true;

        if (!isKeyboardAccessible) {
          issues.push('Embedded content not keyboard accessible');
        }

        const element: MediaElement = {
          type: embed.tagName.toLowerCase() as 'iframe' | 'embed',
          selector: analysis.getElementSelector(embed as HTMLElement),
          src: embed.getAttribute('src') || undefined,
          hasAccessibleControls: true, // Depends on embedded content
          hasTranscript: true, // Cannot determine for embedded content
          hasCaptions: true, // Cannot determine for embedded content
          hasAudioDescription: true, // Cannot determine for embedded content
          hasAltText: true, // Not applicable
          hasProperLabeling,
          isKeyboardAccessible,
          accessibilityScore: 0,
          issues,
          recommendations: [],
          mediaInfo: {
            autoplay: false, // Cannot determine
            controls: true, // Cannot determine
          },
        };

        element.accessibilityScore = analysis.calculateAccessibilityScore(element);
        element.recommendations = analysis.generateRecommendations(element);

        embeddedElements.push(element);
      });

      return embeddedElements;
    }, config);
  }

  /**
   * Calculate overall results
   */
  private calculateOverallResults(
    videoElements: MediaElement[],
    audioElements: MediaElement[],
    imageElements: MediaElement[],
    galleryElements: MediaElement[],
    embeddedContent: MediaElement[],
  ): MediaAnalysisResult {
    const allElements = [
      ...videoElements,
      ...audioElements,
      ...imageElements,
      ...galleryElements,
      ...embeddedContent,
    ];
    const totalElements = allElements.length;
    const accessibleElements = allElements.filter((e) => e.accessibilityScore >= 80).length;

    const overallScore =
      totalElements > 0
        ? Math.round(allElements.reduce((sum, e) => sum + e.accessibilityScore, 0) / totalElements)
        : 100;

    // Collect critical issues
    const criticalIssues: string[] = [];
    allElements.forEach((element) => {
      if (element.accessibilityScore < 50) {
        criticalIssues.push(`${element.type} element has critical accessibility issues`);
      }
      element.issues.forEach((issue) => {
        if (!criticalIssues.includes(issue)) {
          criticalIssues.push(issue);
        }
      });
    });

    // Generate recommendations
    const recommendations: string[] = [
      'Ensure all video content has captions and transcripts',
      'Provide audio descriptions for video content with visual information',
      'Add descriptive alt text to all meaningful images',
      'Make all media controls keyboard accessible',
      'Test media content with screen readers',
    ];

    // Determine compliance level
    let complianceLevel: 'A' | 'AA' | 'AAA' | 'fail' = 'fail';
    if (overallScore >= 90) complianceLevel = 'AAA';
    else if (overallScore >= 80) complianceLevel = 'AA';
    else if (overallScore >= 70) complianceLevel = 'A';

    return {
      totalMediaElements: totalElements,
      accessibleElements,
      videoElements,
      audioElements,
      imageElements,
      galleryElements,
      embeddedContent,
      overallScore,
      criticalIssues,
      recommendations,
      complianceLevel,
    };
  }
}

export default MediaAnalyzer;
